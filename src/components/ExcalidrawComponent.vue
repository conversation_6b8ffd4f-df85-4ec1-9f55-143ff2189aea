<template>
  <div class="excalidraw-container">
    <div v-if="isClient" ref="container" :style="containerStyle">
    </div>
    <div v-else class="loading">
      正在加载绘图工具...
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onUnmounted, nextTick, computed } from 'vue'
import { Excalidraw, type ExcalidrawProps } from '@excalidraw/excalidraw'
import React from 'react'
import ReactDOM from 'react-dom/client'
import '@excalidraw/excalidraw/index.css'

// Props定义
interface Props {
  height?: string | number
  width?: string | number
  theme?: 'light' | 'dark'
  langCode?: string
}

const props = withDefaults(defineProps<Props>(), {
  height: '100vh',
  width: '100%',
  theme: 'light',
  langCode: 'zh-CN'
})

// 响应式数据
const isClient = ref(false)
const container = ref<HTMLElement | null>(null)
let reactRoot: any = null

// 计算样式
const containerStyle = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  border: '1px solid #e1e4e8',
  borderRadius: '8px',
  overflow: 'hidden'
}))

// 事件处理函数
const onChange = (elements: any, appState: any) => {
  console.log('Excalidraw数据变化:', { elements, appState })
  // 这里可以添加数据保存逻辑
}

const onLibraryChange = (libraryItems: any) => {
  console.log('图库变化:', libraryItems)
}

// 组件挂载
onMounted(() => {
  if (typeof window !== 'undefined') {
    isClient.value = true

    nextTick(() => {
      if (container.value) {
        // 创建React根实例
        reactRoot = ReactDOM.createRoot(container.value)

        // Excalidraw配置
        const excalidrawConfig: ExcalidrawProps = {
          theme: props.theme,
          langCode: props.langCode,
          onChange,
          onLibraryChange,
          // 初始数据（可选）
          initialData: {
            appState: {
              viewBackgroundColor: '#ffffff',
              currentItemFontFamily: 1,
            },
            scrollToContent: true,
          },
        }

        // 渲染Excalidraw
        reactRoot.render(React.createElement(Excalidraw, excalidrawConfig))
      }
    })
  }
})

// 组件卸载清理
onUnmounted(() => {
  if (reactRoot) {
    reactRoot.unmount()
    reactRoot = null
  }
})

// 暴露方法给父组件（可选）
defineExpose({
  // 可以暴露一些方法供父组件调用
})
</script>

<style scoped>
.excalidraw-container {
  width: 100%;
  height: 100%;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  font-size: 16px;
  color: #666;
  background-color: #f8f9fa;
  border: 1px solid #e1e4e8;
  border-radius: 8px;
}
</style>