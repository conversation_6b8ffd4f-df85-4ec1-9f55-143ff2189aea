<template>
  <div id="app">
    <header class="app-header">
      <h1>我的绘图应用</h1>
      <p>基于 Excalidraw 的在线绘图工具</p>
    </header>

    <main class="app-main">
      <ExcalidrawComponent
          :height="'calc(100vh - 120px)'"
          theme="light"
          lang-code="zh-CN"
      />
    </main>
  </div>
</template>

<script setup lang="ts">
import ExcalidrawComponent from './components/ExcalidrawComponent.vue'
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: #f8f9fa;
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #e1e4e8;
}

.app-header h1 {
  color: #24292e;
  margin-bottom: 8px;
  font-size: 24px;
  font-weight: 600;
}

.app-header p {
  color: #586069;
  font-size: 14px;
}

.app-main {
  flex: 1;
  padding: 20px;
  background: #ffffff;
}
</style>